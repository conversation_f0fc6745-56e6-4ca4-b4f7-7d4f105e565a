import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';
import { loadEnvironmentWithCustomSchema, provideEnvironment } from '@trendency/kesma-core';
import { VG_ENVIRONMENT, VgEnvironmentSchema } from './environments/environment.definitions';

async function bootstrap(): Promise<void> {
  const env = await loadEnvironmentWithCustomSchema(VgEnvironmentSchema);
  bootstrapApplication(AppComponent, {
    ...appConfig,
    providers: [
      provideEnvironment(env),
      {
        provide: VG_ENVIRONMENT,
        useValue: env,
      },
      ...appConfig.providers,
    ],
  }).catch((err) => console.log(err));
}
bootstrap();
