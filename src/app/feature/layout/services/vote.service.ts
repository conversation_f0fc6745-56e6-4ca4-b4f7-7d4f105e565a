import { Injectable } from '@angular/core';
import { ReCaptchaV3Service } from 'ngx-captcha';
import { ReqService, StorageService, UtilService } from '@trendency/kesma-core';
import { from, mergeMap, Observable } from 'rxjs';
import { VoteData, VoteSubmitResponse } from '@trendency/kesma-ui';
import { injectVgEnvironment } from '../../../../environments/environment.definitions';

const TWO_MONTHS_IN_SECONDS = 5184000;

@Injectable({
  providedIn: 'root',
})
export class VoteService {
  private readonly environment = injectVgEnvironment();

  constructor(
    private readonly reCaptchaV3Service: ReCaptchaV3Service,
    private readonly utilsService: UtilService,
    private readonly reqService: ReqService,
    private readonly storageService: StorageService
  ) {}

  onVotingSubmit(votedId: string, voteData: { data: VoteData; votedId?: string }): void {
    this.preVoting.pipe(mergeMap((token: string) => this.saveVoting(token, votedId))).subscribe((res: VoteSubmitResponse) => {
      voteData.data.answers = res.data.answers;
      if (this.utilsService.isBrowser()) {
        this.storageService.setCookie(`voted_${voteData.data.id}`, votedId, TWO_MONTHS_IN_SECONDS);
      }
      voteData.votedId = String(votedId);
    });
  }

  private get preVoting(): Observable<string> {
    return from(
      new Promise<string>((resolve) => {
        this.reCaptchaV3Service.execute(this.environment.googleSiteKey as string, 'app_publicapi_voting_vote_vote', (token) => {
          resolve(token);
        });
      })
    );
  }

  private saveVoting(token: string, voteId: string): Observable<VoteSubmitResponse> {
    return this.reqService.get(`/voting/vote/${voteId}?`, {
      params: {
        recaptcha: token,
      },
    });
  }

  getVotedId(voteData: VoteData): string | undefined {
    return this.utilsService.isBrowser() ? this.storageService.getCookie(`voted_${voteData.id}`, document.cookie) : undefined;
  }

  getVoteData(value: VoteData): { data: VoteData; votedId?: string } {
    return {
      data: value,
      votedId: value ? this.getVotedId(value) : undefined,
    };
  }
}
