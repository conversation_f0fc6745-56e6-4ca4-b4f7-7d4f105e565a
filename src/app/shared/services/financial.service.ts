import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { add, isAfter } from 'date-fns';
import { tap } from 'rxjs/operators';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';
import { CleanHttpService } from './clean-http.service';
import { FinancialAPIDataCache } from '../definitions';
import { injectVgEnvironment } from '../../../environments/environment.definitions';

@Injectable({
  providedIn: 'root',
})
export class FinancialService {
  private readonly environment = injectVgEnvironment();
  readonly autoRefreshTime: number = 5 * 60; // seconds, should be larger than cacheMaxAge
  readonly cacheMaxAge: number = 60; // seconds
  private dataCache: FinancialAPIDataCache = {};

  constructor(
    protected readonly httpService: CleanHttpService,
    protected readonly utilsService: UtilService
  ) {}

  get financialApiUrl(): string {
    if (typeof this.environment.financialApiUrl === 'string') {
      return this.environment.financialApiUrl as string;
    }

    const { clientApiUrl, serverApiUrl } = this.environment.financialApiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  protected getCachedFinancialData<T>(path: string): Observable<T> {
    if (this.dataCache[path] && isAfter(this.dataCache[path].expireDate, new Date())) {
      return of(this.dataCache[path].data);
    }

    return this.httpService.get<T>(`${this.financialApiUrl}/${path}`, { withCredentials: false }).pipe(
      tap((data: T) => {
        this.dataCache[path] = {
          expireDate: add(new Date(), { seconds: this.cacheMaxAge }),
          data,
        };
      })
    );
  }
}
